# 1. 缓存区

- 使用drawio插件重新绘制[[改善工作流程图.svg]]
- 使用task插件模板片段功能简化核心组件代码
- 使用快捷键`ctrl+s`保存draw.io后报错：Error: Uncaught TypeError: Cannot set properties of null (setting ‘innerHTML’)

# 2. 行动区

- [ ] 分析dataviewjs代码刷新数据后，纵向滚动条自动变化问题 🔼
- [ ] 开发“自动创建任务”脚本（避免反复切换周计划文档添加任务） ⏫
- [ ] 开发excalidraw自定义脚本（自定义选中矩形框宽高） 🔽
- [ ] 开发excalidraw自定义脚本（自定义选中内容字体大小） 🔽
- [ ] 修复「每日执行」“今日阻碍”查询结果间距过大问题 🔼
- [ ] 修复「每周评审」任务统计出现“空任务”问题 ⏫
- [ ] 利用augment优化「阻碍优先级规则说明」文档 🔺
- [ ] 优化阻碍元数据字段值的显示布局（元数据内容较多时，值无法正常垂直居中） 🔼
- [ ] 利用dv.view()方法简化周评审“成果验收”和“本周阻碍”代码、周回顾看板“技术债”和“改善回顾”代码、周计划“技术债”统计代码 🔼
- [ ] 修复“技术债”创建脚本在技术债文件中无法创建问题 ⏫
- [ ] 创建项目核心组件每个模块的使用说明文档 🔺
- [ ] 探索“阻碍”别名的表述中颗粒度的平衡问题 🔼
- [ ] 编写自动创建「输出」、自动建立「输出」双链的JS脚本 🔽
- [ ] 优化阻碍创建JS脚本（笔记属性自动创建关联阻碍链接） 
- [ ] 优化「dataview-table-style.css」功能（表格宽度自适应） 
- [ ] 为「dashboard」添加“折线图”（添加周维度新增阻碍情况） 

# 3. 留观区

- 记录阻碍时，当关联字段的影响对象未创建（不存在）时，应该如何建立链接？
- 阻碍“relation”字段会伴随项目的推进出现影响范围蔓延的问题，是否需要补充关键链接？
- 不论阻碍的类型是阻塞型还是价值威胁类型，从影响对象的角度分析，好像都可以关联周计划和周评审？好像都会影响成果交付？
- 在excallidraw中绘图时，频繁点击鼠标来使用“编辑”功能给工作带了困扰
- 若发现的阻碍（不处理无法继续工作）在2分钟内可以想到解决方案，但是处理结果和花费的时长不确定，那么这个阻碍还需要记录吗？
- 遇到阻碍时在2分钟内能够想到解决方案，但是尝试后发现并不能解决问题，此时阻碍应该记录吗？
- 当前迭代发现成果实现路径错误时，对应观测到的问题应该被作为阻碍记录下来吗？
- 灵感VS下一步思考VS下一步工作？
- 使用脚本创建阻碍时报错：Templater Error:Template parsing error,aborting. check console for more information
- 根据「每日执行」模板新建文件时，提示：Templater Error: No active editor, can't append templates
- ”改善代办“与”闪念“管理的方式十分相似，但是发现的BUG或者其他类型的任务应该怎么办
- 使用JS脚本创建阻碍时，文件名称对应的时间与笔记属性的创建时间不一致
- 在查看“任务安排”中的任务信息时，是否需要显示每条任务的源文件链接？
- 在执行每日任务期间，若当日任务为持续性任务或一日不能完成的任务，那么应该如何记录和安排
- 在obsidian中新建、重新打开文件或重启软件时，鼠标光标的默认位置会影响文件的查看（特别是开头为代码块），不利于查看和编辑文档
- 在excallidraw绘图时，默认的字号类型不能覆盖全部的使用场景，导致部分场景下的图形布局杂乱或畸变
- 在项目推进（迭代）期间，KR进度中已关联的“证据”被优化或删除，原始文件是否需要同时删除？
- 在进行知识版本管理时，通过技术手段使知识组件笔记属性[update_Date]在修改完文件后自动更新，可以直观了解最新的修改状态



