---
created_Date: 2025-08-09
aliases:
  - 技术债模板不可用
position:
  - "[[TP-Project-技术债]]"
type: 系统债
priority: P1
status: 进行中
cssclasses:
  - c3
---
# 1. 基础信息

- 场景描述：根据模板记录技术债时
- 触发条件：
	- 判断技术债类型（`阻碍型/成本型/战略型/无害型`）、优先级（`高/中/低`）
	- 记录基础信息（`发现场景、发现位置、根本原因、关键影响`）
- 可观测现象：1）很难快速得出结论并完成元数据字段填写；2）基础信息填写顺序违背直觉，思维出现多次断裂
- 关键影响：技术债记录中断，后续迭代无法正常开展（有计划偿还债务）

# 2. 应急方案
| 生效时间       | 行动                        | 退出条件     | 状态(≤14d) |
| ---------- | ------------------------- | -------- | -------- |
| 2025-08-09 | 停止记录技术债详细信息，仅简单填写`发生创景`信息 | 模板优化方案上线 | 生效中      |

# 3. 根因分析

- 核心问题是什么？-->模板设计过于理想化
- 为何设计初期没有发现？--> 设计未经过测试

# 4. 偿还计划

- [x] 重新设计模板`type`、`priority`字段 ➕ 2025-08-12 ✅ 2025-08-13
- [x] 重新设计模板正文部分 ➕ 2025-08-12 ✅ 2025-08-13
- [ ] 利用[[td-20250809-01]]测试模板`type`、`priority`字段的合理性 ➕ 2025-08-12
- [ ] 根据测试的成功经验优化「[[TP-Project-技术债]]」 ➕ 2025-08-12

# 5. 债务谱系

```mermaid
graph LR
    A(本债务)
    class D internal-link;
    classDef internal-link fill:#e6f7ff,stroke:#1890ff;
```
# 6. 验收清单

- [ ] 模板每个元数据字段评估时间≤3s
- [ ] 正文模块内容符合直觉流，且未频繁出现思维断裂